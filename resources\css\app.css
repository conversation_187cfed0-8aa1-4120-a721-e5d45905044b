@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    :root {
        --primary: 0 114 244;
        --primary-light: 173 216 255;
        --primary-slate: 240 248 255;
        --client-font: 'Urbanist', sans-serif;
        --admin-font: 'Public Sans', sans-serif;
        --icon-font: 'Iconly';
    }
    body { @apply font-client text-base font-normal scroll-smooth bg-white }

    *::before,
    *::after { @apply inline-block }

    a,
    button,
    form,
    label,
    span { @apply inline-block; font-style: initial; }

    input,
    select,
    textarea {
        @apply
        placeholder:text-sm focus-within:outline-0 bg-transparent text-base placeholder:first-letter:capitalize
    }
}

@layer components {
    /*===============================
            LAYOUTS PART START
    ================================*/
    .container { @apply max-w-6xl mx-auto px-3 sm:px-4 }

    .row { @apply flex flex-wrap -m-3 }
    .col-1 { @apply w-[8.33333333333%] p-3 }
    .col-2 { @apply w-[16.6666666667%] p-3 }
    .col-3 { @apply w-1/4 p-3 }
    .col-4 { @apply w-[33.3333333333%] p-3 }
    .col-5 { @apply w-[41.6666666667%] p-3 }
    .col-6 { @apply w-1/2 p-3 }
    .col-7 { @apply w-[58.3333333333%] p-3 }
    .col-8 { @apply w-[66.6666666667%] p-3 }
    .col-9 { @apply w-3/4 p-3 }
    .col-10 { @apply w-[83.3333333333%] p-3 }
    .col-11 { @apply w-[91.6666666667%] p-3 }
    .col-12 { @apply w-full p-3 }

    .form-row { @apply flex flex-wrap -mx-1 -my-2 }
    .form-col-1 { @apply w-[8.33333333333%] px-1 py-2 }
    .form-col-2 { @apply w-[16.6666666667%] px-1 py-2 }
    .form-col-3 { @apply w-1/4 px-1 py-2 }
    .form-col-4 { @apply w-[33.3333333333%] px-1 py-2 }
    .form-col-5 { @apply w-[41.6666666667%] px-1 py-2 }
    .form-col-6 { @apply w-1/2 px-1 py-2 }
    .form-col-7 { @apply w-[58.3333333333%] px-1 py-2 }
    .form-col-8 { @apply w-[66.6666666667%] px-1 py-2 }
    .form-col-9 { @apply w-3/4 px-1 py-2 }
    .form-col-10 { @apply w-[83.3333333333%] px-1 py-2 }
    .form-col-11 { @apply w-[91.6666666667%] px-1 py-2 }
    .form-col-12 { @apply w-full px-1 py-2 }
    /*===============================
            LAYOUTS PART END
    ================================*/


    /*==================================
            CLIENT SIDE PART START
    ===================================*/
    /* WIDGETS */
    .modal-active { @apply !opacity-100 !visible }
    .modal-active > div { @apply sm:!my-5 }
    .canvas-active { @apply !opacity-100 !visible }
    .canvas-active > div { @apply !translate-x-0 }
    .search-active { @apply !scale-y-100 }
    .language-toggle.active i { @apply ltr:rotate-180 rtl:-rotate-180  }
    .filter-btn.active i { @apply rotate-180 }
    .tab-btn { @apply transition-all duration-500 }
    .tab-div { @apply hidden }
    .tab-div.active { @apply block }
    .tab-btn.active { @apply text-white bg-secondary }
    .profile-link.router-link-active { @apply text-primary bg-[#FFF4F1] }
    .profile-link.router-link-active i { @apply text-primary }
    .cart-switch:checked + label { @apply text-white bg-focus }
    .active-expand { @apply !visible !opacity-100 !scale-y-100 }

    .tab-action { @apply transition-all duration-500 }
    .tab-action.active { @apply !text-white !bg-primary }
    .tab-content { @apply hidden }
    .tab-content.active { @apply block }

    /* ARROWS */
    .indicate { @apply relative after:absolute after:top-2 after:right-2 after:content-[''] after:w-2 after:h-2 after:rounded-full after:shadow-indicate after:bg-primary }
    .down-arrow { @apply after:content-["\e04a"] after:font-icon after:text-sm after:font-semibold flex items-center gap-1.5 }
    .select-arrow { @apply relative after:content-["\e04a"] after:font-icon after:text-xs after:absolute after:top-1/2 after:right-4 after:-translate-y-1/2 after:-mt-0.5 }
    .right-arrow { @apply after:content-["\e048"] after:font-icon after:text-sm flex items-center justify-between gap-1.5 }
    .left-arrow { @apply after:content-["\e049"] after:font-icon after:text-sm flex items-center justify-between gap-1.5 }

    /* TRACKING */
    .order-track-list { @apply flex items-center justify-between px-2 mx-2 md:mx-10 mb-24 relative before:absolute before:top-0 before:left-0 before:w-full before:h-1 before:bg-[#EFF0F6] }
    .order-track-item { @apply relative before:absolute before:-top-1/2 before:left-1/2 before:-translate-y-1/2 before:-translate-x-1/2 before:w-9 before:h-9 before:rounded-full before:border-[4px] before:border-white before:bg-[#D9DBE9] }
    .order-track-item.check { @apply before:bg-[#2AC769] before:content-['\f058'] before:font-icon before:font-bold before:text-white before:text-sm before:leading-7 before:text-center }
    .order-track-status { @apply absolute -bottom-16 left-1/2 -translate-x-1/2 text-sm leading-[18px] text-center capitalize }

    .language-check { @apply appearance-none cursor-pointer checked:before:content-['\f00c'] checked:before:font-icon checked:before:text-xs checked:before:font-bold checked:before:text-primary }

    .action-btn:focus-within + .action-nav { @apply top-14 opacity-100 visible }

    /* SCROLLING */
    .thin-scroll-bar { @apply overflow-y-auto transition-all duration-300 }
    .thin-scroll-bar:hover::-webkit-scrollbar-thumb { @apply bg-slate-500 }
    .thin-scroll-bar::-webkit-scrollbar { @apply w-[3px] }
    .thin-scroll-bar::-webkit-scrollbar { @apply rounded-2xl bg-transparent }

    /* FIELDS */
    .field-title { @apply capitalize text-sm font-medium block mb-1 }
    .field-title.required { @apply after:content-['*'] after:font-medium after:ml-1 after:text-red-500 after:leading-none }
    .field-control { @apply block w-full h-12 font-medium px-4 rounded-lg border transition-all duration-500 bg-white border-gray-200 focus-within:border-primary/30 }
    .multiple-field { @apply flex items-center w-full h-10 rounded-md border transition-all duration-500 bg-white border-gray-200 focus-within:border-primary/30 }
    .multiple-field input { @apply w-1/2 h-full px-3 pb-[2px] border-l first:border-none border-gray-200 transition-all duration-500 }
    .multiple-field button { @apply rounded-md h-8 mr-[3px] px-4 text-base transition-all duration-500 bg-primary/10 text-primary hover:bg-primary hover:text-white }
    .field-alert { @apply ml-1 text-red-500 }
    .field-down-arrow { @apply relative after:content-['\e04a'] after:font-icon after:font-bold after:text-xs after:absolute after:top-1/2 after:-translate-y-1/2 after:right-3 }
    .field-radio-group { @apply flex items-center gap-5 pt-2 }
    .field-radio { @apply flex items-center pt-1 w-fit cursor-pointer }
    .field-button { @apply font-bold text-center w-full px-6 h-12 leading-12 rounded-full bg-primary text-white capitalize }
    .field-checkbox { @apply flex items-center pt-1 w-fit cursor-pointer }
    .field-label { @apply text-[15px] pl-2 leading-4 capitalize cursor-pointer }
    textarea.field-control { @apply h-28 py-3 resize-none }
    .field-control[type=file] { @apply relative font-medium pl-0 text-text transition-all duration-500 before:transition-all before:duration-500 before:content-[''] before:absolute before:top-0 before:left-[115px] before:z-10 before:w-[1px] before:h-full before:bg-gray-200 focus-within:before:bg-primary/30 }
    .field-control[type=file]::-webkit-file-upload-button,
    .field-control[type=file]::file-selector-button { @apply h-12 pl-4 pb-1 pr-10 text-heading rounded-l-lg font-medium cursor-pointer transition-all duration-500 bg-white border-none }

    /* FORMS */
    .form-control { @apply w-full h-12 }
    .form-control .ant-select-selection-placeholder { @apply !leading-11 }
    .form-control .ant-select-selector { @apply !h-full !rounded-lg hover:!border-primary focus-within:!border-primary focus-within:!shadow-none }
    .form-control .ant-select-selection-item { @apply !leading-11 !text-base !text-secondary }
    .form-control input { @apply !h-full !text-base }
    .form-control .anticon-down svg { @apply !h-3 !w-3 }
    .form-control .anticon-search svg { @apply !h-4 !w-4 !-mt-0.5 }

    .form-selection { --selection-round: 8px; --selection-height: 48px; --selection-gap: 16px; }
    .form-selection { @apply relative }
    .form-selection-div { @apply w-full flex items-center gap-[var(--selection-gap)] px-[var(--selection-gap)] rounded-[var(--selection-round)] cursor-pointer border border-[#D9DBE9] transition-all duration-500  }
    .form-selection-div .icon-line-down-arrow { @apply leading-none text-sm -mt-1 cursor-pointer }
    .form-selection-div .icon-line-search { @apply leading-none text-base cursor-pointer }
    .form-selection-div input { @apply w-full h-[var(--selection-height)] cursor-pointer appearance-none text-base font-medium capitalize }
    .form-selection-list { @apply w-full h-full min-h-fit max-h-60 py-1 origin-top scale-y-0 rounded-bl-[var(--selection-round)] rounded-br-[var(--selection-round)] border border-t-0 border-[#D9DBE9] shadow-card overflow-y-auto thin-scroll-bar transition-all duration-500 }
    .form-selection-list li { @apply block text-sm capitalize py-1 px-[var(--selection-gap)] tracking-wide cursor-pointer transition-all duration-500 hover:bg-gray-100 }
    .form-selection:hover .form-selection-div { @apply border-primary/30 }
    .form-selection.active .form-selection-div { @apply cursor-auto rounded-bl-none rounded-br-none border-primary/30 }
    .form-selection.active input { @apply cursor-auto }
    .form-selection.active ul { @apply scale-y-100 border-primary/30 }

    /* CUSTOM */
    .cs-custom-checkbox { --checkbox-size: 16px; }
    .cs-custom-checkbox { @apply cursor-pointer appearance-none relative inline-flex isolate }
    .cs-custom-checkbox::before { @apply content-[''] w-[var(--checkbox-size)] h-[var(--checkbox-size)] rounded inline-block border border-gray-500 transition-all duration-300 }
    .cs-custom-checkbox::after { @apply content-['✓'] absolute w-[var(--checkbox-size)] h-[var(--checkbox-size)] leading-[var(--checkbox-size)] text-center text-xs inset-0 rounded bg-transparent opacity-0 transition-all duration-300 }
    .cs-custom-checkbox:checked { @apply before:border-primary after:bg-primary after:text-white after:opacity-100 }

    .cs-custom-radio { --radio-size: 14px; }
    .cs-custom-radio { @apply cursor-pointer appearance-none relative inline-flex isolate }
    .cs-custom-radio::before { @apply content-[''] w-[var(--radio-size)] h-[var(--radio-size)] rounded-full inline-block bg-transparent border border-solid border-slate-400 transition-all duration-300 }
    .cs-custom-radio::after { @apply content-[''] absolute w-[calc(var(--radio-size)_/_2)] h-[calc(var(--radio-size)_/_2)] top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-transparent transition-all duration-300 }
    .cs-custom-radio:checked { @apply before:border-primary after:bg-primary }

    /* HEADER */
    .header-nav-list { @apply flex items-center justify-center gap-10 }
    .header-nav-item { @apply relative }
    .header-nav-item:hover > div { @apply scale-y-100 }
    .header-nav-menu { @apply capitalize font-semibold py-5 }
    .header-nav-menu.router-link-active { @apply text-primary }

    /* BANNER */
    .banner-swiper .swiper-button-prev { @apply w-8 h-8 leading-8 sm:w-10 sm:h-10 sm:leading-10 shadow-lg text-center rounded-full bg-white text-secondary after:!content-['\e049'] after:font-icon after:text-lg sm:after:text-xl invisible opacity-0 transition-all duration-300 hover:bg-primary hover:after:text-white !left-7 !right-[inherit] }
    .banner-swiper .swiper-button-next { @apply w-8 h-8 leading-8 sm:w-10 sm:h-10 sm:leading-10 shadow-lg text-center rounded-full bg-white text-secondary after:!content-['\e048'] after:font-icon after:text-lg sm:after:text-xl invisible opacity-0 transition-all duration-300 hover:bg-primary hover:after:text-white !right-7 !left-[inherit] }
    .banner-swiper:hover .swiper-button-prev,
    .banner-swiper:hover .swiper-button-next { @apply visible opacity-100 }
    .banner-swiper .swiper-pagination { @apply flex items-center justify-center gap-2.5 !bottom-5 sm:!bottom-7 }
    .banner-swiper .swiper-pagination-bullet { @apply flex-shrink-0 w-3 h-1.5 rounded-xl shadow !m-0 opacity-30 bg-primary transition-all duration-300 }
    .banner-swiper .swiper-pagination-bullet-active { @apply flex-shrink-0 w-4 opacity-100 }

    /* CATEGORY */
    .navigate-swiper { @apply !pt-16 sm:!pt-20 !pb-10 }
    .navigate-swiper .swiper-button-prev { @apply mobile:hidden w-10 h-10 leading-10 text-center rounded-full bg-primary-slate text-primary ltr:after:content-['\e049'] rtl:after:content-['\e048'] after:font-icon after:text-xl transition-all duration-300 hover:bg-primary hover:after:text-white top-6 ltr:left-auto ltr:right-12 rtl:right-auto rtl:left-12 }
    .navigate-swiper .swiper-button-next { @apply mobile:hidden w-10 h-10 leading-10 text-center rounded-full bg-primary-slate text-primary ltr:after:content-['\e048'] rtl:after:content-['\e049'] after:font-icon after:text-xl transition-all duration-300 hover:bg-primary hover:after:text-white top-6 ltr:right-0 rtl:left-0 }
    .navigate-swiper:hover .swiper-button-prev,
    .navigate-swiper:hover .swiper-button-next { @apply visible opacity-100 }

    /* GALLERY */
    .gallery-swiper { @apply px-1 mb-3 }
    .gallery-swiper .swiper-button-prev { @apply w-8 h-8 leading-8 sm:w-10 sm:h-10 sm:leading-10 shadow-lg text-center rounded-full bg-white/50 text-secondary after:content-['\e049'] after:font-icon after:text-lg sm:after:text-xl invisible opacity-0 transition-all duration-300 hover:bg-primary hover:after:text-white !left-5 !right-[inherit] }
    .gallery-swiper .swiper-button-next { @apply w-8 h-8 leading-8 sm:w-10 sm:h-10 sm:leading-10 shadow-lg text-center rounded-full bg-white/50 text-secondary after:content-['\e048'] after:font-icon after:text-lg sm:after:text-xl invisible opacity-0 transition-all duration-300 hover:bg-primary hover:after:text-white !right-5 !left-[inherit] }
    .gallery-swiper .swiper-button-disabled { @apply cursor-not-allowed pointer-events-auto hover:bg-white/50 hover:after:text-secondary }
    .gallery-swiper:hover .swiper-button-prev,
    .gallery-swiper:hover .swiper-button-next { @apply visible opacity-100 }
    .thumb-swiper { @apply px-1 }
    .thumb-swiper .swiper-slide-thumb-active { @apply border-primary }
    .thumb-swiper .swiper-slide-thumb-active img { @apply border-white }
    /*==================================
            CLIENT SIDE PART END
    ==================================*/


    /*===================================
              ADMIN SIDE PART START
    ====================================*/
    /* HEADER */
    .db-header { @apply fixed top-0 left-0 z-30 w-full p-4 flex items-center justify-between lg:gap-36 bg-white transition }
    .db-header-left { @apply flex items-center w-full md:gap-36 }
    .db-header-right { @apply flex-shrink-0 w-fit flex items-center gap-2.5 sm:gap-4 }
    .db-header-logo { @apply flex-auto w-28 flex-shrink-0 }
    .db-header-widget { @apply text-sm w-7 h-7 leading-7 sm:text-base sm:w-8 sm:h-8 sm:leading-8 text-center rounded-lg }
    .db-header-lang { @apply w-4 h-4 rounded-lg mx-auto  }
    .db-header-widget-badge { @apply absolute -top-2 sm:-top-1.5 -right-1 z-10 w-5 h-5 text-center leading-4 rounded-full text-[10px] border-2 border-white text-white bg-primary }
    .db-header-profile-group { @apply flex items-center gap-2 }
    .db-header-profile-group div,
    .db-header-profile-group i { @apply hidden sm:block }
    .db-header-profile-title { @apply font-medium capitalize text-sm leading-none mb-1 }
    .db-header-profile-name { @apply capitalize text-sm leading-none text-heading }
    .db-header-profile-dropdown { @apply absolute top-11 sm:top-12 right-0 z-20 px-2 py-2.5 rounded-lg border border-solid border-slate-200 bg-white shadow-lg }
    .db-header-profile-dropdown-menu { @apply flex items-center justify-start gap-2.5 w-full whitespace-nowrap pl-3 pr-8 py-1.5 text-sm rounded-md capitalize transition text-gray-600 hover:bg-gray-100 }

    /* SIDEBAR */
    .db-sidebar { @apply fixed top-0 ltr:left-0 rtl:right-0 z-[39] w-[260px] p-4 h-dvh ltr:-translate-x-[100%] rtl:translate-x-full ltr:lg:translate-x-0 rtl:lg:-translate-x-0 lg:top-16 lg:h-[calc(100vh_-_64px)] overflow-y-auto thin-scrolling ltr:shadow-db-sidebar-right rtl:shadow-db-sidebar-left transition bg-white }
    .db-sidebar.active { @apply ltr:translate-x-0 rtl:-translate-x-0 ltr:lg:-translate-x-[100%] rtl:lg:translate-x-full shadow-sidebar lg:shadow-none }
    .db-sidebar-header { @apply flex lg:hidden items-center justify-between mb-8 }
    .db-sidebar-nav { @apply mb-6 last:mb-0 font-medium }
    .db-sidebar-nav-title { @apply uppercase text-xs mb-3 font-medium tracking-[2px] text-gray-400 }
    .db-sidebar-nav-item.active .db-sidebar-nav-menu { @apply text-primary bg-primary/5 }
    .db-sidebar-nav-item.router-link-active .db-sidebar-nav-menu { @apply text-primary bg-primary/5 }
    .db-sidebar-nav-menu.router-link-active { @apply text-primary bg-primary/5 }
    .db-sidebar-nav-menu { @apply capitalize w-full flex items-center justify-start text-left rounded-lg gap-3 py-2.5 px-3 transition hover:bg-gray-100 }
    .db-sidebar-nav-menu i { @apply text-xl }
    .db-sidebar-nav-menu span { @apply text-base flex-auto ltr:text-left rtl:text-right }
    .db-sidebar-nav-dropdown { @apply h-0 overflow-hidden transition }
    .db-sidebar-nav-menu.downarrow { @apply after:content-['\f054'] after:font-awesome after:font-bold after:text-[10px] after:transition }
    .db-sidebar-nav-item.active .downarrow { @apply after:rotate-90 }
    .db-sidebar-nav-dropdown-list { @apply mx-5 my-2 flex flex-col border-l-2 border-gray-100 }
    .db-sidebar-nav-dropdown-menu { @apply pl-5 pr-2 py-2 rounded-lg text-sm leading-4 capitalize transition hover:bg-gray-100 hover:text-primary }

    /* MESSAGE */
    .db-message-list .active { @apply !bg-primary/5 }
    .db-message-list .active h4 { @apply text-primary }

    /* CHATS */
    .chat-list { @apply flex flex-col-reverse overflow-x-hidden overflow-y-auto gap-5 p-4 transition }
    .chat-list.frontend { @apply h-[calc(100vh_-_260px)] sm:h-[calc(100vh_-_250px)] }
    .chat-list.backend { @apply h-[400px] sm:h-[calc(100vh_-_315px)] }
    .chat-list.frontend.change { @apply h-[calc(100vh_-_320px)] sm:h-[calc(100vh_-_300px)] }
    .chat-list.backend.change { @apply h-[400px] sm:h-[calc(100vh_-_380px)] }
    .chat-item { @apply flex items-end gap-3 }
    .chat-avatar { @apply w-7 h-7 mb-5 rounded-full flex-shrink-0 }
    .chat-group { @apply flex flex-col gap-1 }
    .chat-group-text { @apply flex flex-col gap-1 }
    .chat-text { @apply w-fit max-w-sm text-sm py-3 px-4 text-heading }
    .chat-group-meta { @apply flex flex-wrap items-center justify-between gap-1 }
    .chat-meta { @apply text-xs }

    .chat-user { @apply flex-row-reverse }
    .chat-admin .chat-text { @apply rounded-2xl ltr:odd:rounded-bl ltr:even:rounded-tl rtl:odd:rounded-br rtl:even:rounded-tr bg-[#EFF0F6] }
    .chat-user .chat-text { @apply rounded-2xl ltr:even:rounded-tr ltr:odd:rounded-br rtl:even:rounded-tl rtl:odd:rounded-bl bg-primary-light }
    .chat-user .chat-group-text { @apply items-end }
    .chat-user .chat-group-meta { @apply flex-row-reverse }

    .chat-list::-webkit-scrollbar { @apply w-2 bg-transparent }
    .chat-list::-webkit-scrollbar-thumb { @apply rounded-xl bg-gray-400 border-2 border-solid border-white }

    .chat-footer { @apply w-full flex items-center gap-5 p-4 }
    .chat-footer-file-label { @apply flex-shrink-0 relative isolate cursor-pointer w-fit h-fit }
    .chat-footer-file-input { @apply absolute top-0 left-0 opacity-0 w-full h-full -z-10 }
    .chat-footer-data { @apply w-full rounded-3xl py-2 px-4 overflow-hidden bg-[#F7F7FC] }
    .chat-footer-data-list { @apply flex gap-3 py-2 overflow-x-auto }
    .chat-footer-data-list::-webkit-scrollbar { @apply h-2 bg-gray-200 rounded-xl }
    .chat-footer-data-list::-webkit-scrollbar-thumb { @apply rounded-xl bg-gray-400 border-2 border-solid border-slate-200 }
    .chat-footer-data-input { @apply h-full w-full py-2 }
    .chat-footer-sent { @apply flex-shrink-0 }

    /* THIN SCROLL BAR */
    .thin-scrolling { @apply overflow-y-auto transition }
    .thin-scrolling:hover::-webkit-scrollbar-thumb { @apply bg-primary }
    .thin-scrolling::-webkit-scrollbar { @apply w-[3px] }
    .thin-scrolling::-webkit-scrollbar { @apply rounded-2xl bg-transparent }

    /* OTHERS */
    .db-image { @apply w-full rounded-[10px] border-[5px] border-white shadow }
    .db-light-text { @apply text-base font-light tracking-wide }

    /* ORDER STATUS */
    .db-order-status.check { @apply before:bg-primary before:content-['\f00c'] before:font-awesome before:font-bold before:text-white before:text-xs before:text-center }
    .db-order-status.active { @apply before:border-[5px] }

    /* COMMON CLASS */
    .invalid { @apply !border-red-300 before:!bg-red-300 }

    /* MAIN CONTENT */
    .db-main { @apply px-3 sm:px-4 pb-4 xh:pt-[170px] pt-[80px] lg:pt-[84px] ltr:lg:pl-[280px] rtl:lg:pr-[280px] ltr:lg:pr-5 rtl:lg:pl-5 lg:pb-5 h-dvh overflow-y-auto transition text-paragraph bg-[#f7f7fc] }
    .db-main.expand { @apply ltr:lg:pl-5 rtl:lg:pr-5 }

    /* BACKLDROP */
    .backdrop { @apply w-full h-full fixed top-0 left-0 z-40 invisible opacity-0 bg-black/60 transition }
    .backdrop.active { @apply visible opacity-100 }

    /* BUTTON */
    .db-btn { @apply inline-flex items-center justify-center gap-2 rounded-md shadow-db-card capitalize text-sm font-normal py-2.5 px-4 transition }
    .db-btn-full { @apply flex items-center justify-center gap-2 w-full rounded-lg shadow-db-card capitalize text-sm font-medium py-2.5 px-4 transition }
    .xmark-btn { @apply w-6 h-6 leading-6 text-base text-center rounded-full transition bg-gray-100 text-gray-500 hover:text-primary hover:bg-red-100 }
    .db-btn-outline { @apply inline-flex items-center justify-center gap-1.5 rounded-md capitalize text-sm font-normal py-1.5 px-3 border transition border-gray-200 text-gray-700 bg-white hover:border-primary/20 hover:bg-primary/5 hover:text-primary }
    .db-btn-outline i { @apply text-xs }
    .db-btn-outline.primary { @apply border-primary text-primary hover:bg-primary/10 }
    .db-btn-outline.warning { @apply border-[#2196F3] text-[#2196F3] hover:bg-[#2196F3]/10 }
    .db-btn-outline.success { @apply border-green-500 text-green-600 hover:bg-green-50 }
    .db-btn-outline.danger { @apply border-red-500 text-red-600 hover:bg-red-50 }
    .db-btn-outline.info { @apply border-cyan-500 text-cyan-600 hover:bg-cyan-50 }
    .db-btn-outline.sm { @apply !text-xs !rounded !py-1 !px-2 !gap-1 }
    .db-btn-outline.sm i { @apply !text-[10px] }

    .db-btn-fill { @apply inline-flex items-center justify-center gap-1.5 text-sm capitalize shadow-db-card py-1.5 px-3 rounded-md transition }
    .db-btn-fill i { @apply text-xs }
    .db-btn-fill.primary { @apply text-primary bg-primary/10 hover:bg-primary/20 }
    .db-btn-fill.yellow { @apply text-[#2196F3] bg-[#2196F3]/10 hover:bg-[#2196F3]/20 }
    .db-btn-fill.purple { @apply text-fuchsia-600 bg-fuchsia-100 hover:bg-fuchsia-200 }
    .db-btn-fill.green { @apply text-emerald-600 bg-emerald-100 hover:bg-emerald-200 }
    .db-btn-fill.blue { @apply text-blue-600 bg-blue-100 hover:bg-blue-200 }
    .db-btn-fill.gray { @apply text-gray-600 bg-gray-200 hover:bg-gray-300 }
    .db-btn-fill.red { @apply text-red-600 bg-red-100 hover:bg-red-200 }
    .db-btn-fill.sm { @apply text-xs rounded py-1 px-2 gap-1 }
    .db-btn-fill.sm i { @apply text-[10px] }

    /* CARDS */
    .db-card { @apply rounded border-none shadow-db-card bg-white }
    .db-card-body { @apply p-5 }
    .db-card-header { @apply p-5 flex flex-wrap justify-center flex-col items-center sm:flex-row sm:justify-between border-b border-gray-200 gap-5 }
    .db-card-title { @apply capitalize font-medium text-[18px] text-paragraph }
    .db-card-filter { @apply flex flex-wrap items-center justify-center sm:items-start gap-2.5 }
    .db-card-filter-btn { @apply flex items-center justify-start gap-1.5 pt-2 pb-1.5 px-2.5 text-sm leading-none capitalize rounded-md transition border text-paragraph border-primary bg-white hover:text-primary hover:border-primary/10 hover:bg-primary/5 after:content-['\f107'] after:text-primary after:font-awesome after:font-bold after:text-[11px] after:-mt-1 }
    .db-card-filter-select { @apply cursor-pointer h-9 ltr:pl-2.5 ltr:pr-7 rtl:pr-2.5 rtl:pl-7 text-sm leading-none capitalize appearance-none rounded-md transition border text-primary border-primary bg-white after:content-['\f107'] after:font-awesome after:font-bold after:text-[11px] after:-mt-1 }
    .db-card-filter-btn i { @apply text-sm text-primary  }
    .db-card-filter-btn span { @apply ltr:pr-1 rtl:pl-1 text-primary }
    .db-card-filter .db-field-down-arrow { @apply text-primary }
    .db-card-filter-dropdown-list { @apply absolute top-10 right-0 z-20 p-2 rounded-md bg-white shadow-lg }
    .db-card-filter-dropdown-menu { @apply flex items-center whitespace-nowrap justify-start gap-2.5 px-3 py-1.5 text-sm rounded-md capitalize transition text-gray-500 hover:text-primary hover:bg-primary/5 }
    .db-card-filter-dropdown-menu i { @apply w-4 flex-shrink-0 }

    /* BREADCRUMB */
    .db-breadcrumb { @apply flex flex-col flex-wrap gap-y-2 gap-x-5 items-center justify-center sm:flex-row sm:justify-between pt-[14px] }
    .db-breadcrumb-title { @apply capitalize text-xl font-semibold }
    .db-breadcrumb-list { @apply flex flex-wrap items-center justify-center sm:justify-end }
    .db-breadcrumb-item { @apply capitalize text-[22px] text-slate-400 }
    .db-breadcrumb-link { @apply transition text-slate-600 hover:text-primary after:content-['/'] after:px-1.5 after:text-paragraph }

    /* TABS */
    .db-tab-btn { @apply w-full flex items-center text-[15px] capitalize text-left gap-3 py-2 px-3 rounded border-b last:border-none transition border-slate-100 hover:bg-slate-100 }
    .db-tab-btn i { @apply text-lg }
    .db-tab-btn.active { @apply !text-primary !bg-primary/5 }
    .db-tab-div { @apply hidden }
    .db-tab-div.active { @apply block }
    .db-tab-sub-div { @apply hidden }
    .db-tab-sub-div.active { @apply block }
    .db-tab-sub-btn.active { @apply !text-white !bg-primary }
    #orderPayment .db-tab-btn { @apply last:!border-solid }
    #orderPayment .db-tab-btn.active { @apply !text-primary !bg-primary/5 border border-primary}

    .db-tabBtn { @apply w-full flex items-center justify-center gap-2 rounded sm:rounded-b-none sm:rounded-t capitalize py-2.5 sm:py-3 px-3 text-sm border border-transparent sm:border-b-primary/50 transition hover:bg-gray-100 hover:text-primary }
    .db-tabBtn.active { @apply text-primary border-primary/50 sm:border-b-white bg-primary/5 sm:bg-white }
    .db-tabDiv { @apply hidden rounded sm:rounded-t-none sm:rounded-b py-3 px-4 sm:py-4 sm:px-5 text-sm leading-6 shadow-db-card border sm:border-x sm:border-b border-primary/50 sm:border-t-white bg-white }
    .db-tabDiv.active { @apply block }
    .db-tabBtn span { @apply whitespace-nowrap text-ellipsis overflow-hidden }

    .profile-tabBtn.active { @apply shadow-[0px_8px_15px_rgba(253,_139,_14,_0.18)] stroke-white text-white bg-primary }
    .profile-tabDiv { @apply hidden }
    .profile-tabDiv.active { @apply block }

    /* DROPDOWN */
    .dropdown-group { @apply relative leading-[0px] }
    .dropdown-btn { @apply cursor-pointer }
    .dropdown-list { @apply scale-y-0 origin-top }
    .dropdown-list.active { @apply scale-y-100 origin-top }

    /* FIELDS */
    .db-field-title { @apply uppercase text-xs font-medium block mb-2 text-[#566a7f] }
    .db-field-title.required { @apply after:content-['*'] ltr:after:ml-1 rtl:after:mr-1 after:font-medium after:text-base after:text-red-500 after:leading-none }
    .db-field-control { @apply block w-full text-sm h-10 px-3 rounded-md border transition bg-white border-gray-200 focus-within:border-primary/20 }
    .db-multiple-field { @apply flex items-center w-full h-10 rounded-md border transition bg-white border-gray-200 focus-within:border-primary/20 }
    .db-multiple-field input { @apply w-1/2 h-full px-3 pb-[2px] ltr:border-l rtl:border-r first:border-none border-gray-200 transition }
    .db-multiple-field button { @apply rounded-md h-8 ltr:mr-[3px] rtl:ml-[3px] px-4 text-base transition bg-primary/10 text-primary hover:bg-primary hover:text-white }
    .db-group-field { @apply flex items-center w-full h-10 rounded-md border transition bg-white border-gray-200 focus-within:border-primary/20 }
    .db-group-field input { @apply w-full h-full px-3 pb-[2px] ltr:border-r rtl:border-l border-gray-200 transition text-sm }
    .db-group-field button { @apply h-full px-3 text-base transition hover:text-primary }
    .db-field-alert { @apply ml-1 text-red-500 }
    .db-field-down-arrow { @apply relative after:content-['\f107'] after:font-awesome after:font-bold after:text-xs after:absolute after:top-1/2 after:-translate-y-1/2 ltr:after:right-3 rtl:after:left-3 }
    .db-field-radio-group { @apply flex items-center gap-5 pt-2 }
    .db-field-radio { @apply flex items-center pt-1 w-fit cursor-pointer }
    .db-field-checkbox { @apply flex items-center pt-1 w-fit cursor-pointer }
    .db-field-label { @apply text-[15px] ltr:pl-2 rtl:pr-2 leading-4 capitalize cursor-pointer }
    textarea.db-field-control { @apply h-36 py-3 }
    .db-field-control[type=file] { @apply relative text-sm ltr:pl-0 rtl:pr-0 transition before:transition before:content-[''] before:absolute before:top-0 ltr:before:left-[107px] rtl:before:right-[113px] before:z-10 before:w-[1px] before:h-full before:bg-gray-200 focus-within:before:bg-primary/20 }
    .db-field-control[type=file]::-webkit-file-upload-button,
    .db-field-control[type=file]::file-selector-button { @apply h-10 pl-4 pb-1 pr-7 rounded-l-lg text-sm text-paragraph cursor-pointer transition bg-white border-none }
    .checkbox:checked { @apply border-none }
    .checkbox:checked + .check-icon { @apply flex }
    .checkbox:checked + .checkmark { @apply bg-primary border-primary }

    input:checked + div { @apply border-primary }
    input:checked + div svg { @apply block }

    .custom-switch { @apply inline-flex items-center justify-center cursor-pointer }
    .custom-switch input { @apply relative w-5 h-3 cursor-pointer
    before:content-[''] before:inline-block before:absolute before:inset-0 before:w-full before:h-full before:rounded-3xl before:bg-paragraph before:transition-all before:duration-300
    after:content-[''] after:absolute after:top-1/2 after:left-[30%] after:-translate-y-1/2 after:-translate-x-1/2 after:w-1.5 after:h-1.5 after:rounded-full after:bg-white after:transition-all after:duration-300 }
    .custom-switch input:checked { @apply before:bg-primary after:left-[70%] }
    .custom-switch label { @apply cursor-pointer ltr:pl-2 rtl:pr-2 text-sm uppercase text-paragraph transition-all duration-300 }
    .custom-switch input:checked + label { @apply text-primary }

    .custom-checkbox { @apply relative w-4 h-4 cursor-pointer }
    .custom-checkbox-field { @apply absolute z-10 opacity-0 w-full h-full appearance-none cursor-pointer }
    .custom-checkbox-icon { @apply absolute top-0 left-0 w-full h-full text-center rounded leading-[14px] text-[10px] border cursor-pointer border-[#d9dee3] text-white }
    .custom-checkbox-field:checked + .custom-checkbox-icon { @apply bg-primary border-primary shadow-checkRound }

    .custom-radio { @apply relative w-4 h-4 cursor-pointer }
    .custom-radio-field { @apply absolute z-10 opacity-0 w-full h-full appearance-none cursor-pointer }
    .custom-radio-span { @apply absolute top-0 left-0 w-full h-full text-center rounded-full border cursor-pointer border-[#d9dee3] }
    .custom-radio-field:checked + .custom-radio-span { @apply border-[5px] bg-white border-primary shadow-checkRound }

    .custom-radio.sm { @apply w-3 h-3 }
    .custom-radio.sm .custom-radio-field:checked + .custom-radio-span { @apply border-[3px] }

    .shopperz-quileditor .ql-toolbar,
    .shopperz-quileditor .ql-container { @apply font-client text-base border-gray-200 }
    .shopperz-quileditor .ql-toolbar { @apply rounded-t-lg }
    .shopperz-quileditor .ql-container { @apply h-32 rounded-b-lg }
    .shopperz-quileditor .ql-container.ql-snow { @apply border-gray-200 }

    /* TABLE */
    .db-table-responsive { @apply w-full overflow-x-auto }
    .db-table { @apply table-auto w-full text-left whitespace-nowrap }
    .db-table.stripe .db-table-body-tr { @apply odd:!bg-[#f9fafb] }
    .db-table-head { @apply ltr:text-left rtl:text-right border-b-2 border-t border-gray-200 }
    .db-table-body { @apply ltr:text-left rtl:text-right }
    .db-table-head-th { @apply px-4 py-3 tracking-[1px] font-medium text-xs uppercase text-[#566a7f] }
    .db-table-body-tr { @apply border-b border-gray-200 last:border-none }
    .db-table-body-td { @apply px-4 py-3 text-sm }
    .db-table-badge { @apply py-1 px-2 rounded-md capitalize }
    .db-table-action { @apply relative }
    .db-table-action i { @apply text-sm p-2 py-1 m-0.5 rounded transition }
    .db-table-action.view i { @apply text-primary bg-primary/10 hover:bg-primary/20 }
    .db-table-action.edit i { @apply text-green-500 bg-green-100 hover:bg-green-200 }
    .db-table-action.delete i { @apply text-red-500 bg-red-100 hover:bg-red-200 }
    .table-filter-div { @apply h-0 overflow-hidden transition-all duration-500 ease-linear }

    /* TOOLTIP */
    .db-tooltip {
        @apply absolute left-1/2 -translate-x-1/2 text-[10px] whitespace-nowrap font-medium tracking-wide uppercase pt-[1px] pb-[2px] px-[7px] rounded bg-gray-700 text-white
        after:content-[''] after:absolute after:-bottom-[3px] after:left-1/2 after:-translate-x-1/2 after:rotate-45 after:border-solid after:border-t-4 after:border-4 after:rounded-sm
        after:border-t-transparent after:border-l-transparent after:border-r-gray-700 after:border-b-gray-700
        -top-5 opacity-0 invisible transition
    }

    .db-table-action:hover .db-tooltip { @apply opacity-100 visible -top-full }

    /* MODAL */
    .modal { @apply fixed top-0 left-1/2 -translate-x-1/2 z-60 invisible opacity-0 w-full h-full overflow-y-auto transition bg-black/60 }
    .modal.active { @apply visible opacity-100 }
    .modal-dialog { @apply w-full mx-auto max-w-lg rounded-lg transition bg-white }
    .modal.active .modal-dialog { @apply my-10 }
    .modal-header { @apply flex justify-between gap-5 p-4 border-b border-slate-200 }
    .modal-title { @apply capitalize text-lg font-medium }
    .modal-body { @apply p-4 }
    .modal-footer { @apply p-4 border-t border-slate-200 }
    .modal-btns { @apply flex flex-wrap justify-end gap-3 mt-3 }
    .modal-btn-fill { @apply inline-flex items-center gap-1.5 capitalize !text-sm py-1.5 px-3 rounded-md border border-primary text-white bg-primary }
    .modal-btn-outline { @apply inline-flex items-center gap-1.5 capitalize !text-sm py-1.5 px-3 rounded-md border border-gray-200 !text-gray-600 bg-white }
    .modal-close { @apply leading-none text-xl text-[#FB4E4E]  }

    /* PAGINATION */
    .db-pagination-navigate { @apply flex items-center gap-2 capitalize rounded-md border px-4 py-2 text-sm font-medium border-gray-300 bg-white text-gray-700 transition hover:bg-gray-50 }
    .db-pagination-nav { @apply flex w-fit items-center overflow-hidden rounded-lg border border-gray-200 }
    .db-pagination-menu { @apply px-3.5 py-2 text-sm text-center font-medium ltr:border-r rtl:border-l last:border-none border-gray-200 text-gray-600 transition hover:bg-gray-100 }
    .db-pagination-menu.active { @apply text-primary bg-primary/20 }
    .db-pagination-menu i { @apply text-xs }

    /* LIST */
    .db-list { @apply flex flex-col -my-1.5 }
    .db-list-item { @apply flex flex-col items-start sm:flex-row sm:items-center py-1.5 rounded-md transition }
    .db-list-item-title { @apply ltr:text-left rtl:text-right text-sm capitalize font-semibold whitespace-nowrap after:inline-block sm:after:hidden after:content-[':'] after:pl-1 text-heading }
    .db-list-item-text { @apply ltr:text-left rtl:text-right text-sm }

    .db-list.single .db-list-item-title { @apply w-full max-w-full sm:max-w-[180px] }
    .db-list.single .db-list-item-text { @apply w-full }

    .db-list.multiple { @apply flex-row flex-wrap }
    .db-list.multiple .db-list-item { @apply w-full sm:w-1/2 }
    .db-list.multiple .db-list-item-title { @apply w-full sm:w-1/2 }
    .db-list.multiple .db-list-item-text { @apply w-full sm:w-1/2 }

    /* BADGE */
    .db-badge { @apply text-xs tracking-wide uppercase py-1 px-2 rounded }
    .db-badge.yellow { @apply text-yellow-600 bg-yellow-100 }
    .db-badge.green { @apply text-green-600 bg-green-100 }
    .db-badge.blue { @apply text-blue-600 bg-blue-100 }
    .db-badge.red { @apply text-red-600 bg-red-100 }

    /* DRAWER */
    .drawer { @apply fixed top-0 right-0 translate-x-[100%] z-50 w-full max-w-xl h-dvh overflow-y-auto transition bg-white }
    .drawer.active { @apply translate-x-[0%] }
    .drawer-header { @apply flex justify-between gap-5 p-4 border-b border-slate-200 }
    .drawer-title { @apply capitalize text-lg font-medium }
    .drawer-body { @apply px-5 py-8 }

    /* DB PRODUCT CARD */
    .db-product-cart:hover svg,
    .db-product-cart:hover span { @apply fill-white text-white }

    .db-pos-cartDiv { @apply translate-x-full md:translate-x-0 transition }
    .db-pos-cartDiv.active { @apply translate-x-0 }

    .pos-group .active { @apply bg-primary-light border-primary }

    /* FOOTER */
    .footer-part { @apply bg-primary }

    /* INSTALLER */
    .installer-track { @apply flex items-center justify-between w-[109%] sm:w-[111%] }
    .installer-track li { @apply flex items-center w-full }
    .installer-track li:last-child::after { @apply hidden }
    .installer-track li:after { @apply content-[''] w-full h-[1px] bg-[#D9DBE9] }
    .installer-track li i { @apply flex-shrink-0 w-8 h-8 leading-[30px] text-xs sm:w-[38px] sm:h-[38px] sm:leading-9 sm:text-sm text-center rounded-full border text-white border-[#D9DBE9] bg-[#D9DBE9] }
    .installer-track li.done { @apply cursor-pointer }
    .installer-track li.done::after { @apply bg-primary }
    .installer-track li.done i { @apply bg-white border-primary text-primary }
    .installer-track li.active i { @apply bg-primary border-primary text-white }

    .unread-message h4 { @apply relative after:content-[''] after:absolute after:top-0 ltr:after:-right-3 rtl:after:-left-3 after:inline-block after:w-2 after:h-2 after:rounded-full after:bg-primary  }
}

@layer utilities {
    .transition { @apply transition-all duration-300 ease-linear }
}

.swiper:hover .banner-swiper-navigate { @apply opacity-100 }
.swiper-pagination-bullet { @apply text-xs font-normal w-5 h-5 leading-5 text-center rounded-full shadow-md opacity-100 !m-0 text-paragraph bg-white }
.swiper-pagination-bullet-active { @apply bg-primary text-white }


input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
    -moz-appearance:textfield;
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration { display: none; }

.apexcharts-tooltip { @apply !border !border-gray-200 !rounded-md !shadow-paper overflow-hidden }
.apexcharts-tooltip-z-group,
.apexcharts-tooltip-goals-group { @apply !hidden }
.apexcharts-tooltip-title { @apply !py-1.5 !px-3 !mb-0 !border-b !border-gray-200 !bg-white }
.apexcharts-tooltip-series-group { @apply !py-1.5 !px-3 }

.time-slot-gap { margin: 12px; }

.btnr { @apply py-1.5 px-2 shadow-[0px_3px_8px_rgba(0,0,0,0.24)] bg-white rounded-md cursor-pointer w-full flex items-center justify-center}
#cursor { @apply text-3xl inline-block pt-2.5}
.btnr:active{@apply bg-[rgb(177,176,176)]}
.btnr:hover{@apply bg-[rgb(233,228,228)]}
.btnr::selection {
    user-select: none;
}


