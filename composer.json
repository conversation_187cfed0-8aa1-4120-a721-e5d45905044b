{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "ext-http": "*", "2checkoutv2/2checkout-php-sdk": "^1.0", "anandsiddharth/laravel-paytm-wallet": "^2.0", "aws/aws-sdk-php-laravel": "^3.9", "barryvdh/laravel-dompdf": "^3.0", "cashfree/cashfree-pg": "4.2", "dipesh79/laravel-phonepe": "*", "dipokhalder/laravel-env-editor": "^1.0", "doctrine/dbal": "^3.7", "google/apiclient": "^2.16", "guzzlehttp/guzzle": "^7.2", "iyzico/iyzipay-php": "^2.0", "karim007/laravel-bkash-tokenize": "^2.2", "kingflamez/laravelrave": "^4.2", "laravel/framework": "^10.10", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "league/flysystem-aws-s3-v3": "^3.29", "maatwebsite/excel": "*", "mollie/laravel-mollie": "^2.25", "nyawach/laravel-pesapal": "^1.1", "obydul/laraskrill": "^1.2", "picqer/php-barcode-generator": "^2.4", "pragmarx/countries": "0.8.2", "razorpay/razorpay": "2.*", "santigraviano/laravel-mercadopago": "^1.0", "silviolleite/laravelpwa": "^2.0", "smartisan/laravel-settings": "^2.0", "spatie/laravel-medialibrary": "^10.0.0", "spatie/laravel-permission": "^5.10", "srmklive/paypal": "~3.0", "staudenmeir/laravel-adjacency-list": "^1.0", "stripe/stripe-php": "^12.7", "twilio/sdk": "^7.9", "unicodeveloper/laravel-paystack": "^1.1", "vonage/client": "^4.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}