import activityEnum from "./modules/activityEnum";
import askEnum from "./modules/askEnum";
import dateFormatEnum from "./modules/dateFormatEnum";
import encryptionEnum from "./modules/encryptionEnum";
import statusEnum from "./modules/statusEnum";
import timeFormatEnum from "./modules/timeFormatEnum";
import currencyPositionEnum from "./modules/currencyPositionEnum";
import otpDigitLimitEnum from "./modules/otpDigitLimitEnum";
import otpExpireTimeEnum from "./modules/otpExpireTimeEnum";
import otpTypeEnum from "./modules/otpTypeEnum";
import analyticSectionEnum from "./modules/analyticSectionEnum";
import roleEnum from './modules/roleEnum';
import inputTypeEnum from './modules/inputTypeEnum';
import menuSectionEnum from "./modules/menuSectionEnum";
import promotionTypeEnum from "./modules/promotionTypeEnum";
import shippingMethodEnum from "./modules/shippingMethodEnum";
import shippingTypeEnum from "./modules/shippingTypeEnum";
import switchEnum from "./modules/switchEnum";
import returnStatusEnum from "./modules/returnStatusEnum";
import addressTypeEnum from "./modules/addressTypeEnum";
import discountTypeEnum from "./modules/discountTypeEnum";
import purchasePaymentMethodEnum from "./modules/purchasePaymentMethodEnum";
import purchasePaymentStatusEnum from "./modules/purchasePaymentStatusEnum";
import posPaymentMethodEnum from "./modules/posPaymentMethodEnum";


export default {
    activityEnum,
    askEnum,
    dateFormatEnum,
    encryptionEnum,
    otpDigitLimitEnum,
    otpExpireTimeEnum,
    otpTypeEnum,
    statusEnum,
    timeFormatEnum,
    analyticSectionEnum,
    roleEnum,
    inputTypeEnum,
    menuSectionEnum,
    currencyPositionEnum,
    promotionTypeEnum,
    shippingMethodEnum,
    shippingTypeEnum,
    switchEnum,
    returnStatusEnum,
    addressTypeEnum,
    discountTypeEnum,
    purchasePaymentMethodEnum,
    purchasePaymentStatusEnum,
    posPaymentMethodEnum
};
