{"menu": {"license": "License", "create": "Create", "edit": "Edit", "view": "View", "dashboard": "Dashboard", "settings": "Settings", "company": "Company", "mail": "Mail", "otp": "OTP", "notification": "Notification", "social_media": "Social Media", "cookies": "Cookies", "analytics": "Analytics", "analytic_section": "Analytic Section", "theme": "Theme", "sliders": "Sliders", "currencies": "Currencies", "product_categories": "Product Categories", "product_attributes": "Product Attributes", "product_attribute_options": "Product Attribute Options", "taxes": "Taxes", "pages": "Pages", "languages": "Languages", "sms_gateway": "Sms Gateway", "payment_gateway": "Payment Gateway", "site": "Site", "role": "Role", "role_permissions": "Role & Permissions", "products": "Products", "product_and_stock": "Product & Stock", "pos_and_orders": "POS & Orders", "promo": "Promo", "communications": "Communications", "users": "Users", "accounts": "Accounts", "reports": "Reports", "setup": "Setup", "purchase": "Purchase", "stock": "Stock", "pos": "POS", "pos_orders": "POS Orders", "online_orders": "Online Orders", "coupons": "Coupons", "offers": "Offers", "push_notifications": "Push Notifications", "administrators": "Administrators", "customers": "Customers", "employees": "Employees", "transactions": "Transactions", "sales_report": "Sales Report", "products_report": "Products Report", "credit_balance_report": "Credit Balance Report", "overview": "Overview", "account_info": "Account info", "change_password": "Change Password", "order_history": "Order History", "return_orders": "Return Orders", "damages": "Damages", "address": "Address", "product_brands": "Product Brands", "units": "Units", "variations": "Variations", "settings_menu": "<PERSON><PERSON><PERSON>", "product_video": "Product Video", "seo": "SEO", "promotions": "Promotions", "product_sections": "Product Sections", "benefits": "Benefits", "suppliers": "Suppliers", "profile": "Profile", "password": "Password", "addresses": "Addresses", "wishlist": "Wishlist", "login": "Log in", "shipping_setup": "Shipping Setup", "order_area": "Order Area", "notification_alert": "Notification Alert", "order_statistics": "Order Statistics", "top_products": "Top Products", "return_reasons": "Return Reasons", "order_details": "Order Details", "write_review": "Write a Review", "product_review": "Product Review", "pos_order_receipt": "Pos Order Review", "order_receipt": "Order Receipt", "edit_profile": "Edit Profile", "outlets": "Outlets", "return_and_refunds": "Return And Refunds", "subscribers": "Subscribers", "add_payment": "Add Payment", "purchase_payments": "Purchase Payments", "countries": "Countries", "states": "States", "cities": "Cities", "location_setup": "Location Setup", "pwa": "PWA", "progressive_web_app": "Progressive Web App", "whatsapp_order_setup": "Whatsapp Order Setup"}, "label": {"license_code": "License Code", "previous": "Previous", "next": "Next", "sign_in": "Sign In", "email": "Email", "password": "Password", "remember_me": "Remember me", "hello": "Hello", "name": "Name", "phone": "Phone", "website": "Website", "city": "City", "state": "State", "country_code": "Country Code", "zip_code": "Zip Code", "address": "Address", "date_format": "Date Format", "time_format": "Time Format", "default_timezone": "Default Timezone", "default_currency": "<PERSON><PERSON><PERSON>", "default_language": "Default Language", "language_switch": "Language Switch", "online_payment_gateway": "Online Payment Gateway", "auto_update": "Auto Update", "app_debug": "App Debug", "default_sms_gateway": "Default Sms Gateway", "android_app_link": "Android App Link", "ios_app_link": "Ios App Link", "copyright": "Copyright", "google_map_key": "Google Map Key", "digit_after_decimal_point": "Digit After Decimal Point", "ex": "( Ex: 0.00 )", "currency_position": "Currency Position", "email_verification": "Email Verification", "phone_verification": "Phone Verification", "mail_host": "Mail Host", "mail_port": "Mail Port", "mail_username": "Mail Username", "mail_password": "Mail Password", "mail_encryption": "Mail Encryption", "mail_from_name": "Mail From Name", "mail_from_email": "Mail From Email", "ssl": "SSL", "tls": "TLS", "otp_digit_limit": "Otp Digit Limit", "otp_expire_time": "Otp Expire Time", "limit_per_user": "Limit Per User", "otp_type_checking": "Otp Type", "otp_type": "Otp Type", "notification_fcm_secret_key": "Firebase Secret Key", "notification_fcm_public_vapid_key": "Firebase Public VAPID Key (Key Pair)", "notification_fcm_api_key": "Firebase Api Key", "notification_fcm_auth_domain": "Firebase Auth Domain", "notification_fcm_project_id": "Firebase Project Id", "notification_fcm_storage_bucket": "Firebase Storage Bucket", "left": "Left", "right": "Right", "notification_fcm_messaging_sender_id": "Firebase Message Sender Id", "password_confirmation": "Password Confirmation", "basic_info": "Basic Information", "change_password": "Change Password", "orders": "Orders", "label": "Label", "full_name": "Full Name", "admin": "Admin", "add_customer": "Add Customer", "customer": "Customer", "barcode": "Barcode", "tags": "Tags", "information": "Information", "images": "Images", "variation": "Variation", "size": "Size", "transaction_id": "Transaction Id", "date": "Date", "payment_method": "Payment Method", "order_serial_no": "Order Serial No", "amount": "Amount", "role": "Role", "balance": "Balance", "discount_percentage": "Discount Percentage", "products": "Products", "product": "Product", "price": "Price", "video": "Video", "additional_price": "Additional Price", "user": "User", "push_notification": "Push Notification", "video_provider": "Video Provider", "product_video": "Product Video", "notification_fcm_app_id": "Firebase App Id", "notification_fcm_measurement_id": "Firebase Measurement Id", "notification": "Notification", "facebook": "Facebook", "youtube": "Youtube", "instagram": "Instagram", "twitter": "Twitter", "cookies_details_page": "Cookies Details Page", "cookies_summary": "Cookies Summary", "status": "Status", "action": "Action", "active": "Active", "inactive": "Inactive", "header": "Header", "footer": "Footer", "data": "Data", "section": "Section", "logo": "Logo", "fav_icon": "Fav Icon", "footer_logo": "Footer <PERSON>", "title": "Title", "slider": "Slide<PERSON>", "image": "Image", "description": "Description", "link": "Link", "symbol": "Symbol", "is_cryptocurrency": "Is Cryptocurrency", "exchange_rate": "Exchange Rate", "yes": "Yes", "no": "No", "default": "<PERSON><PERSON><PERSON>", "code": "Code", "tax_rate": "Tax Rate", "permissions": "Permissions", "members": "Members", "create": "Create", "update": "Update", "delete": "Delete", "view": "View", "page": "Page", "menu_section_id": "Menu Section", "template_id": "<PERSON><PERSON>", "files": "Files", "language": "Language", "sms_gateway": "Sms Gateway", "enable": "Enable", "disable": "Disable", "live": "Live", "sandbox": "Sandbox", "twilio_account_sid": "<PERSON><PERSON><PERSON> Account <PERSON>", "twilio_auth_token": "<PERSON><PERSON><PERSON>", "twilio_from": "<PERSON><PERSON><PERSON>", "twilio_service_id": "Twilio Service Id", "twilio_status": "Twilio <PERSON>", "paypal_app_id": "Paypal App Id", "paypal_mode": "Paypal Mode", "paypal_client_id": "Paypal Client Id", "paypal_client_secret": "Paypal Client Secret", "paypal_status": "Paypal Status", "stripe_key": "Stripe Key", "stripe_secret": "Stripe Secret", "stripe_status": "Stripe Status", "stripe_mode": "Stripe Mode", "flutterwave_public_key": "Flutterwave Public Key", "flutterwave_secret_key": "Flutterwave Secret Key", "flutterwave_mode": "Flutterwave Mode", "flutterwave_status": "Flutterwave Status", "paystack_public_key": "paystack Public Key", "paystack_secret_key": "paystack Secret Key", "paystack_payment_url": "Paystack Payment Url", "paystack_mode": "Paystack Mode", "paystack_status": "Paystack Status", "sslcommerz_store_name": "Sslcommerz Store Name", "sslcommerz_store_id": "Sslcommerz Store Id", "sslcommerz_store_password": "Sslcommerz Store Password", "sslcommerz_mode": "Sslcommerz Mode", "sslcommerz_status": "Sslcommerz Status", "mollie_api_key": "<PERSON><PERSON>", "mollie_mode": "<PERSON><PERSON>", "mollie_status": "Mollie Status", "senangpay_merchant_id": "Senangpay Merchant Id", "senangpay_secret_key": "Senangpay Secret Key", "senangpay_mode": "Senangpay Mode", "senangpay_status": "Senangpay Status", "bkash_app_key": "BKash App Key", "bkash_app_secret": "BKash App Secret", "bkash_username": "BKash Username", "bkash_password": "BKash Password", "bkash_mode": "BKash Mode", "bkash_status": "BKash Status", "paytm_merchant_id": "Paytm Merchant Id", "paytm_merchant_key": "Paytm Merchant Key", "paytm_merchant_website": "Paytm Merchant Website", "paytm_channel": "Paytm Channel", "paytm_industry_type": "Paytm Industry Type", "paytm_mode": "Paytm Mode", "paytm_status": "Paytm Status", "razorpay_key": "Razorpay Key", "razorpay_secret": "Razorpay Secret", "razorpay_status": "Razorpay Status", "razorpay_mode": "Razorpay Mode", "mercadopago_client_id": "Mercadopago Client Id", "mercadopago_client_secret": "Mercadopago Client Secret", "mercadopago_mode": "Mercadopago Mode", "mercadopago_status": "Mercadopago Status", "cashfree_app_id": "Cashfree App Id", "cashfree_secret_key": "Cashfree Secret Key", "cashfree_mode": "Cashfree Mode", "cashfree_status": "Cashfree Status", "payfast_merchant_id": "Payfast Merchant Id", "payfast_merchant_key": "Payfast Merchant Key", "payfast_passphrase": "Payfast Passphrase", "payfast_mode": "Payfast Mode", "payfast_status": "Payfast Status", "skrill_merchant_email": "Skrill Merchant Email", "skrill_merchant_api_password": "Skrill Merchant Api Password", "skrill_mode": "Skrill Mode", "skrill_status": "Skrill Status", "more_gateway": "More Gateway", "latitude": "Latitude", "longitude": "Longitude", "product_attribute_option": "Product Attribute Option", "parent_category": "Parent Category", "category": "Category", "buying_price": "Buying Price", "selling_price": "Selling <PERSON>", "tax": "Tax", "show_stock_out": "Show Stock Out", "can_purchasable": "Can Purchasable", "refundable": "Refundable", "maximum_purchase_quantity": "Maximum Purchase Quantity", "low_stock_quantity_warning": "Low Stock Quantity Warning", "weight": "Weight", "brand": "Brand", "unit": "Unit", "save": "Save", "old_password": "Old Password", "new_password": "New Password", "confirm_password": "Confirm Password", "home": "Home", "offers": "Offers", "search": "Search", "discount": "Discount", "discount_type": "Discount Type", "minimum_order": "Minimum Order Amount", "maximum_discount": "Maximum Discount", "start_date": "Start Date", "end_date": "End Date", "coupon": "Coupon", "fixed": "Fixed", "percentage": "Percentage", "or": "Or", "your_email_address": "Your email address", "contact": "Contact", "support": "Support", "body": "Body", "legal": "Legal", "sku": "SKU", "ex_tag": "tag1,tag2,tag3", "shipping_and_return": "Shipping & Return", "purchasable": "Purchasable", "browse_by_categories": "Browse by Categories", "explore_all_products": "Explore All Products", "products_found": "Products Found", "seo": "SEO", "meta_keyword": "<PERSON>a Keyword", "small": "Small", "big": "Big", "type": "Type", "slug": "Slug", "sort": "Sort", "more": "More", "flash_sale": "Flash Sale", "offer": "Offer", "offer_start_date": "Offer Start Date", "offer_end_date": "Offer End date", "add_to_flash_sale": "Do you want to add in the flash sale?", "filter_and_sorting": "Filter & Sorting", "reviews": "Reviews", "newest": "Newest", "price_low_to_high": "Price Low To High", "price_high_to_low": "Price High To Low", "top_rated": "Top Rated", "sort_by": "Sort By", "product_found": "Product Found", "review": "Review", "show_more": "Show More", "categories": "Categories", "offer_products": "offer Products", "company": "Company", "reference_no": "Reference No", "attachments": "Attachments", "supplier": "Supplier", "add_products": "Add Products", "purchase_items": "Purchase Items", "purchases": "Purchases", "more_options": "More Options", "order_tax": "Order Tax", "shipping": "Shipping", "payment_term": "Payment Term", "note": "Note", "unit_cost": "Unit Cost", "sub_total": "SubTotal", "actions": "Actions", "taxes": "Taxes", "product_tax": "Product Tax", "total": "Total", "details": "Details", "product_details": "Product Details", "product_shipping_and_return": "Product Shipping & Return", "product_reviews": "Product Reviews", "read_more": "Read More", "videos": "Videos", "product_videos": "Product Videos", "related_products": "Related Products", "available": "Available", "stock_out": "Stock Out", "total_price": "Total Price", "shopping_cart": "Shopping Cart", "quantity": "Quantity", "pending": "Pending", "ordered": "Ordered", "received": "Received", "account_information": "Account Information", "personal_info": "Personal Info", "email_address": "Email Address", "phone_number": "Phone Number", "upload_image": "Upload Image", "street_address": "Street Address", "add_new_address": "Add New Address", "country": "Country", "use_phone_instead": "Use Phone Instead", "use_email_instead": "Use Email Instead", "sign_up": "Sign Up", "enter_the_code_sent_to": "Enter The Code Sent To", "back_to_sign_in": "Back to Sign In", "verify_number": "Verify Number", "verify_email": "<PERSON><PERSON><PERSON>", "not_receive_code": "If you didn't receive a code!", "display_mode": "Display Mode", "ltr": "LTR", "rtl": "RTL", "location": "Location", "reference": "Reference", "select_one": "Select One", "wishlist": "Wishlist", "my_wishlist": "My Wishlist", "follow_us": "Follow Us", "item": "<PERSON><PERSON>", "subtotal": "Subtotal", "items": "Items", "your_shipping_cart": "Your Shipping Cart", "cart": "<PERSON><PERSON>", "checkout": "Checkout", "payment": "Payment", "order_summery": "Order Summery", "shipping_charge": "Shipping Charge", "delivery": "Delivery", "pick_up": "Pick Up", "store_location": "Store Location", "shipping_address": "Shipping Address", "billing_address": "Billing Address", "coupon_code": "Coupon Code", "default_shipping_method": "Default Shipping Method", "select_shipping_method": "Select Shipping Method", "product_wise": "Product Wise", "flat_rate_wise": "Flat Rate Wise", "distance_wise": "Distance Wise", "area_wise": "Area Wise", "shipping_cost": "Shipping Cost", "free_delivery_kilometer": "Free Delivery Kilometer", "basic_delivery_charge": "Basic Delivery Charge", "charge_per_kilo": "Charge Per <PERSON>", "free": "Free", "flat_rate": "Flat Rate", "shipping_type": "Shipping Type", "is_product_quantity_multiply": "Is Product Quantity Multiply", "return_order": "Return Orders", "reason": "Reason", "cash_on_delivery": "Cash On Delivery", "mail": "Mail", "sms": "Sms", "messages": "Messages", "off": "Off", "on": "On", "damages": "Damages", "select_payment_method": "Select Payment Method", "order_history": "Order History", "order_id": "Order ID", "download": "Download", "paid": "Paid", "unpaid": "Unpaid", "confirmed": "Confirmed", "on_the_way": "On The Way", "delivered": "Delivered", "canceled": "Canceled", "rejected": "Rejected", "default_shipping_cost": "Default Shipping Cost", "order_details": "Order Details", "tax_fee": "Tax Fee", "order_summary": "Order Summary", "order_date": "Order Date", "order_status": "Order Status", "payment_status": "Payment Status", "order_pending": "Order Pending", "order_confirmed": "Order Confirmed", "order_on_the_way": "Order On The Way", "order_delivered": "Order Delivered", "shipment": "Shipment", "order_cancelled": "Order Cancelled", "showing": "Showing", "to": "to", "of": "of", "results": "results", "total_earnings": "Total Earnings", "total_orders": "Total Orders", "total_customers": "Total Customers", "total_products": "Total Products", "processing": "Processing", "out_for_delivery": "Out For Delivery", "returned": "Returned", "top_customers": "Top Customers", "reminder": "Reminder!", "data_reset": "Dummy data will be reset in every 30 minutes.", "ongoing": "Ongoing", "sales_summary": "Sales Summary", "total_sales": "Total Sales", "avg_sales_per_day": "Avg Sales Per Day", "sales": "Sales", "orders_summary": "Orders Summary", "customer_stats": "Customer Stats", "select_date_range": "Select Date Range", "non_purchase_product_maximum_quantity": "Non Purchase Product Maximum Quantity", "return_request": "Return Request", "return_reason": "Return Reason", "return_note": "Return Note", "attachment": "Attachment", "return_orders": "Return Orders", "return_order_details": "Return Order Details", "return_response": "Return Response", "accepted": "Accepted", "welcome_back": "Welcome Back", "total_completed": "Total Completed", "total_returned": "Total Returned", "wallet_balance": "Wallet Balance", "show_full_history": "Show Full History", "order_type": "Order Type", "payment_type": "Payment Type", "delivery_information": "Delivery Information", "source": "Source", "web": "Web", "app": "App", "pos": "Pos", "paid_status": "Paid Status", "see_order_details": "See Order Details", "back_to_orders": "Back to Orders", "payment_info": "Payment Info", "method": "Method", "your_review": "Your Review", "review_details": "Review Details", "upload_images": "Upload Images", "add_image": "Add Image", "cash": "Cash", "qty": "qty", "product_description": "Product Description", "powered_by": "Powered by", "order_date_time": "Order Date Time", "search_customer": "Search Customer", "select_customer": "Select Customer", "search_category": "Search Category", "select_category": "Select Category", "search_brand": "Search Brand", "select_brand": "Select Brand", "search_here": "Search Here...", "product_variation": "Product Variation", "add_discount": "Add Discount", "not_refundable": "Not Refundable", "forgot_password": "Forgot Password", "back_to_signin": "Back To Sign In", "get_otp": "Get OTP", "verification": "Verification", "enter_otp": "Enter O<PERSON>p", "please_select": "Please Select", "outlet": "Outlet", "sold_quantity": "Sold Quantity", "reset_password": "Reset Password", "accept": "Accept", "return_details": "Return Details", "reject_reason": "Reject Reason", "popular_brands": "Popular Brands", "order_rejected": "Order Rejected", "about_our_privacy": "About Our Privacy", "cookies_settings": "<PERSON><PERSON> Settings", "most_popular": "Most Popular", "small_size": "Small Size", "big_size": "Big Size", "is_return_product_price_add_to_credit": "Is Return Product Price Add To Credit", "pick_up_address": "Pick Up Address", "subject": "Subject", "message": "Message", "created_by": "Created By", "created_date": "Created Date", "tel": "Tel", "file": "File", "cheque": "Cheque", "credit_card": "Credit Card", "others": "Others", "partial_paid": "Partial Paid", "fully_paid": "<PERSON>y Paid", "phonepe_merchant_id": "Phonepe Merchant Id", "phonepe_merchant_user_id": "Phonepe Merchant User Id", "phonepe_key_index": "Phonepe Key Index", "phonepe_key": "Phonepe Key", "phonepe_status": "Phonepe Status", "phonepe_mode": "Phonepe Mode", "clickatell_apikey": "Clickatell Apikey", "clickatell_status": "Clickatell Status", "nexmo_key": "Nexmo Key", "nexmo_secret": "Nexmo Secret", "nexmo_status": "Nexmo Status", "msg91_key": "MSG91 Auth Key", "msg91_sender_id": "MSG91 Sender Id", "msg91_template_id": "MSG91 Template Id", "msg91_status": "MSG91 Status", "twofactor_api_key": "2Factor Api Key", "twofactor_module": "2Factor <PERSON><PERSON><PERSON>", "twofactor_from": "2Factor From", "twofactor_status": "2Factor Status", "bulksms_username": "Bulksms Username", "bulksms_password": "Bulksms Password", "bulksms_status": "Bulksms Status", "bulksmsbd_api_key": "Bulksmsbd Api key", "bulksmsbd_sender_id": "Bulksmsbd Sender Id", "bulksmsbd_status": "Bulksmsbd Status", "telesign_api_key": "Telesign Api Key", "telesign_customer_id": "Telesign Customer Id", "telesign_sender_id": "Telesign Sender Id", "telesign_status": "Telesign Status", "manager": "Manager", "pos_operator": "POS Operator", "splash": "Splash", "icon": "Icon", "install_app": "Install App", "whatsapp_number": "Whatsapp Number", "json": "Json", "telr_store_id": "Telr Store Id", "telr_store_auth_key": "Telr Store Auth key", "telr_mode": "Telr Mode", "telr_status": "Telr Status", "iyzico_api_key": "Iyzico Api Key", "iyzico_secret_key": "Iyzico Secret Key", "iyzico_mode": "Iyzico Mode", "iyzico_status": "Iyzico Status", "pesapal_consumer_key": "Pesapal Consumer Key", "pesapal_consumer_secret": "Pesapal Consumer Secret", "pesapal_ipn_id": "Pesapal IPN Id", "pesapal_mode": "Pesapal Mode", "pesapal_status": "Pesapal Status", "midtrans_server_key": "Midtrans Server Key", "midtrans_mode": "Midtrans Mode", "midtrans_status": "Midtrans Status", "upload_file": "Upload File", "xlsx": "xls/xlsx", "enter_card_last_4_digits": "Enter Card Last 4 Digits", "enter_transaction_id": "Enter Transaction ID", "enter_payment_note": "Enter Payment Note", "card": "Card", "mobile_banking": "Mobile Banking", "other": "Other", "total_discounts": "Total Discounts", "total_shipping_charges": "Total Shipping Charges", "total_categories": "Total Categories", "total_sold_quantity": "Total Sold Quantity", "warranty": "Warranty", "mfs": "MFS", "input_amount": "Input Amount", "order_payment": "Order Payment", "total_amount": "Total Amount", "enter_note": "Enter Note", "change": "Change", "msg91_template_variable": "MSG91 Template Variable", "twocheckout_seller_id": "Twocheckout seller id", "twocheckout_secret_key": "Twocheckout secret key", "twocheckout_buy_link_secret_word": "twocheckout buy link secret word", "twocheckout_mode": "Twocheckout mode", "twocheckout_status": "Twocheckout status"}, "button": {"add": "Add", "save": "Save", "edit": "Edit", "delete": "Delete", "view": "View", "close": "Close", "cancel": "Cancel", "clear": "Clear", "search": "Search", "filter": "Filter", "export": "Export", "print": "Print", "excel": "XLS", "reset": "Reset", "logout": "Logout", "edit_profile": "Edit Profile", "change_password": "Change Password", "add_analytics": "Add Analytics", "add_analytic_section": "Add Analytic Section", "add_slider": "<PERSON><PERSON>", "add_currency": "Add <PERSON>cy", "add_product_category": "Add Product Category", "add_page": "Add Page", "add_language": "Add Language", "get_file_content": "Get File Content", "add_product_attribute": "Add Product Attribute", "add_product_attribute_option": "Add Product Attribute Option", "add_administrator": "Add Administrator", "upload_new_photo": "Upload New Photo", "profile": "Profile", "security": "Security", "addresses": "Addresses", "my_orders": "My Orders", "add_address": "Add Address", "address": "Address", "add_customer": "Add Customer", "add_employee": "Add Employee", "add_product_brand": "Add Product Brand", "add_unit": "Add Unit", "upload_new_image": "Upload New Image", "add_offer": "<PERSON>d Offer", "add_variation": "Add Variation", "add_push_notification": "Add Push Notification", "add_video": "Add Video", "add_role": "Add Role", "permissions": "Permissions", "add_product": "Add Product", "add_tax": "Add Tax", "add_coupon": "Add Coupon", "register_your_account": "Register your account", "login_to_your_account": "Login to your account", "subscribe": "Subscribe", "add_promotion": "Add Promotion", "add_product_section": "Add Product Section", "add_benefit": "Add Benefit", "favorite": "Favorite", "add_to_cart": "Add to Cart", "add_supplier": "Add Supplier", "add_purchase": "Add Purchase", "add_return": "Add Return", "add_damage": "Add Damage", "save_changes": "Save Changes", "add_new_address": "Add New Address", "resend_code": "Resend Code", "verify": "Verify", "remove": "Remove", "process_to_checkout": "Process to Checkout", "apply": "Apply", "add_new": "Add New", "save_address": "Save Address", "back_to_cart": "Back to Cart", "save_and_pay": "Save and Pay", "add_order_area": "Add Order Area", "back_to_checkout": "Back to Checkout", "confirm_order": "Confirm Order", "download_receipt": "Download Receipt", "cancel_order": "Cancel Order", "return_request": "Return Request", "write_review": "Write Review", "see_your_order_details": "See your order details", "add_return_reason": "Add Return Reason", "submit_return": "Submit Return", "accept": "Accept", "reject": "Reject", "submit_review": "Submit Review", "edit_review": "Edit Review", "print_invoice": "Print Invoice", "order": "Order", "add_outlet": "Add Outlet", "submit": "Submit", "decline": "Decline", "send_mail": "Send Mail", "add_payment": "Add Payment", "view_payments": "View Payments", "go_back": "Go Back", "let_me_check": "Let me check", "add_country": "Add Country", "add_state": "Add State", "add_city": "Add City", "install": "Install", "whatsapp": "Whatsapp", "proceed_to_whatsapp": "Proceed To WhatsApp", "import": "Import", "sample_file": "Sample File", "upload_file": "Upload File", "pdf": "PDF", "barcode": "Barcode", "confirm_and_print_receipt": "Confirm & Print Receipt"}, "number": {"10": "10", "25": "25", "50": "50", "100": "100", "500": "500", "1000": "1000", "403": "403", "404": "404"}, "error": {"something_wrong": "Something Wrong."}, "message": {"add": "Add", "app_to_your_home_screen": "app to your home screen", "pagination_label": "Showing {from} to {to} of {total} entries", "continue_shopping": "Sign in to continue shopping", "dont_have_account": "Don't have an account?", "image_upload": "Image upload successfully.", "image_delete": "Image delete successfully.", "image_update": "Image updated successfully.", "photo_update": "Photo updated successfully.", "profile_update": "Profile update successfully", "password_update": "Password update successfully", "subscribe_to_our_newsletter": "Subscribe to our newsletter", "subscribe": "Thank you for subscribing.", "shipping_and_return": "Shipping and return update successfully", "product_offer": "Product Offer update successfully", "selection_message": "Please select these before adding any product", "already_have_account": "Already have an account?", "add_to_cart": "Successfully add to cart", "maximum_quantity": "You already added the maximum quantity", "checkout_guide": "Shipping, Taxes & Discount Calculate At Checkout", "provide_your_shipping_information": "Provide Your Shipping Information", "check_your_information_before_you_continue": "Check your information before you continue", "payment_information": "Payment Information", "select_your_payment_method": "Payment Information", "apply_coupon": "Apply coupon code", "get_discount_with_your_order": "Get discount with your order", "coupon_add": "Coupon Add Successfully.", "you_saved": "You saved {amount}", "coupon_applied": "Coupon applied.", "coupon_delete": "Coupon Delete Successfully.", "save_shipping_address_as_a_billing_address": "Save shipping address as a billing address", "shipping_and_billing_address": "Shipping and billing address is required.", "attachment_not_found": "attachment not found.", "thank_you": "Thank You", "order_status_follows": "Your Order status is as follows", "payment_method_required": "The payment method is required.", "good_morning": "Good Morning!", "good_afternoon": "Good Afternoon!", "good_evening": "Good Evening!", "thank_you_for_your_order": "Thank you for your order.", "your_order_is_successfully_placed": "Your order is successfully placed.", "please_come_again": "Please Come Again", "discount_error_message": "Discount less then or equal to 100", "discount_fixed_error_message": "The discount amount does not over the subtotal amount", "pos_order": "Pos Order Created successfully", "empty_variation_message": "The attribute is empty. Create attribute and attribute option.", "email_send": "Email Send Successfully.", "email_send_failed": "Email Send Failed !! Please Check Your Mail Configuration.", "page_not_found": "Page Not Found!", "we_can_not_seem": "We can't seem to find the page you're looking for.", "access_denied": "Access Denied!", "you_try_to_access": "You tried to access a page you did not have prior authorization for.", "please_check_your_order_list": "Please check your order list.", "for_quick_demo": "For quick demo login click below", "file_update_success": "File update successfully", "pwa_image_remainder": "Please be patient while optimizing the images for various devices. This may take a moment as they are resized to ensure the best experience across different screens.Kindly refrain from reloading the page during this process to avoid any disruptions", "coupon_remove": "The coupon has been removed", "app_debug_disabled": "App debug is disabled in demo mode", "purchase_limit_exceeded": "The maximum purchase quantity limit exceeded.", "barcode_not_found": "The barcode not found."}}