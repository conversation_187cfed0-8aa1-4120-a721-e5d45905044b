APP_NAME=Laravel
APP_ENV=local
APP_KEY=base64:e7gBPVzThPSkHj28dl+67KoSh7dMYnhyOpgQCAJWSDE=
APP_DEBUG=true
APP_URL=http://localhost

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=laravel
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_ROOT=
AWS_URL=

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

FCM_SECRET_KEY=AAAAR-ItgeQ:APA91bHSRHexFge83tV33p9xiV0qyQ-naPZJj8TnCM9xg9gq4c_fyn30LP-x81SEnQDTbPFrqiMHkf7WSYnpM18Zb9uccmJX2wI6d1-DhAB13Kf6WFoUsPeDd07MdAEKTauVAGUD_d_J
FCM_TOPIC=

DEMO=true
TIMEZONE=Asia/Dhaka

MIX_HOST="${APP_URL}"
MIX_API_KEY=SHOP-123456-KING
MIX_DEMO="${DEMO}"

CURRENCY=USD
CURRENCY_SYMBOL=$
CURRENCY_POSITION=5
CURRENCY_DECIMAL_POINT=2

DATE_FORMAT=d-m-Y
TIME_FORMAT="h:i A"

DISPLAY_TYPE=fashion
NON_PURCHASE_QUANTITY=100
MEDIA_DISK=public
